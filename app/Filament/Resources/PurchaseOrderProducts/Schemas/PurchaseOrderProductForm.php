<?php

namespace App\Filament\Resources\PurchaseOrderProducts\Schemas;

use App\Filament\Forms\Components\BelongsToSelect;
use App\Filament\Schemas\Components\PriceField;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class PurchaseOrderProductForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->components([
                BelongsToSelect::make('product_id')
                    ->label(__('Product'))
                    ->relationship(name: 'product', titleAttribute: 'name')
                    ->required()
                    ->searchable(),

                TextInput::make('quantity')
                    ->label(__('Quantity'))
                    ->required()
                    ->numeric()
                    ->minValue(0.01),

                PriceField::make()
                    ->priceFieldName('buy_price')
                    ->currencyFieldName('buy_price_currency')
                    ->priceFieldLabel(__('Buy Price'))
                    ->currencyFieldLabel(__('Currency'))
                    ->required(),
            ]);
    }

    public static function getRepeaterSchema(): array
    {
        return [
            BelongsToSelect::make('product_id')
                ->label(__('Product'))
                ->relationship(name: 'product', titleAttribute: 'name')
                ->required()
                ->searchable()
                ->columnSpan(1),

            TextInput::make('quantity')
                ->label(__('Quantity'))
                ->required()
                ->numeric()
                ->minValue(0.01)
                ->columnSpan(1),

            PriceField::make()
                ->priceFieldName('buy_price')
                ->currencyFieldName('buy_price_currency')
                ->priceFieldLabel(__('Buy Price'))
                ->currencyFieldLabel(__('Currency'))
                ->required()
                ->columnSpan(1),
        ];
    }
}
