<?php

namespace App\Filament\Resources\Suppliers\Schemas;

use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class SupplierForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->components([
                TextInput::make('name')
                    ->label(__('Name'))
                    ->required()
                    ->maxLength(255),

                TextInput::make('email')
                    ->label(__('Email'))
                    ->required()
                    ->email()
                    ->maxLength(255),

                TextInput::make('phone')
                    ->label(__('Phone'))
                    ->required()
                    ->maxLength(255),

                Textarea::make('address')
                    ->label(__('Address'))
                    ->rows(3),

                Select::make('products')
                    ->label(__('Products'))
                    ->relationship('products', 'name')
                    ->multiple()
                    ->searchable()
                    ->preload(),
            ]);
    }
}
