<?php

namespace App\Filament\Resources\PurchaseOrders\Schemas;

use App\Filament\Forms\Components\BelongsToSelect;
use App\Filament\Resources\PurchaseOrderProducts\Schemas\PurchaseOrderProductForm;
use Filament\Forms\Components\Repeater;
use Filament\Schemas\Schema;

class PurchaseOrderForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->components([
                BelongsToSelect::make('supplier_id')
                    ->label(__('Supplier'))
                    ->relationship(name: 'supplier', titleAttribute: 'name')
                    ->required()
                    ->searchable(),

                Repeater::make('purchaseOrderProducts')
                    ->label(__('Purchase Order Products'))
                    ->relationship()
                    ->schema(PurchaseOrderProductForm::getRepeaterSchema())
                    ->columns(3)
                    ->reorderable(false)
                    ->addActionLabel(__('Add Product'))
                    ->minItems(1),
            ]);
    }
}
