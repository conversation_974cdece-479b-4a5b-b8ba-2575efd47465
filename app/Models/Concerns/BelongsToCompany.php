<?php

namespace App\Models\Concerns;

use App\Models\Company;
use Filament\Facades\Filament;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

trait BelongsToCompany
{
    public static function bootBelongsToCompany()
    {
        static::creating(function (Model $model) {
            $model->incremental_id = DB::table($model->getTable())
                ->where('company_id', Filament::getTenant()?->id)
                ->max('incremental_id') + 1;

            $model->company_id = Filament::getTenant()?->id;
        });
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }
}
